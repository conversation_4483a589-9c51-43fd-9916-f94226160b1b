import { tool } from 'ai';
import { z } from 'zod';

export const sendEmailVerification = tool({
  description: 'Send a verification email code to the user',
  parameters: z.object({
    email: z.string(),
  }),
  execute: async ({ email }) => {
    console.log('Sending verification code to', email);

    // Create an AbortController with a timeout of 30 seconds for email operations
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000);

    try {
      const response = await fetch(
        `${process.env.SERVER_HOST}/api/v1/onboarding/send_verification_code`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.SERVER_API_KEY}`,
          },
          body: JSON.stringify({
            onboarding: {
              email,
            },
          }),
          signal: controller.signal,
        },
      );

      // Clear the timeout as the request completed
      clearTimeout(timeoutId);

      // Check response status - only 200 and 201 are considered success
      if (response.status === 200 || response.status === 201) {
        const result = await response.json();
        console.log('Verification code sent to', email, result);

        // Add success flag if not present
        return { ...result, success: true };
      } else {
        // Handle non-successful status codes (4XX and 5XX)
        console.error(
          `Server returned non-success status ${response.status} ${response.statusText}`,
        );
        return {
          error: `Failed to send verification code. Please try again later.`,
          success: false,
        };
      }
    } catch (error) {
      // Clear the timeout if there was an error
      clearTimeout(timeoutId);

      console.error('Error in email verification:', error);

      // Specific error for timeout
      if (error instanceof Error && error.name === 'AbortError') {
        return {
          error: 'Failed to send verification code. Please try again later.',
          success: false,
        };
      }

      // General error response
      return {
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
        success: false,
      };
    } finally {
      // Make sure timeout is cleared in all cases
      clearTimeout(timeoutId);
    }
  },
});
