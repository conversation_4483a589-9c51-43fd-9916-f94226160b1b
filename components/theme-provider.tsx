'use client';

import { ThemeProvider as NextThemesProvider } from 'next-themes';
import type { ThemeProviderProps } from 'next-themes/dist/types';

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      forcedTheme="light"
      enableSystem={false}
      defaultTheme="light"
      {...props}
    >
      {children}
    </NextThemesProvider>
  );
}
