'use client';

import type { <PERSON><PERSON><PERSON>Value, UIMessage } from 'ai';
import cx from 'classnames';
import Image from 'next/image';
import { AnimatePresence, m, motion } from 'framer-motion';
import { memo, useState } from 'react';
import type { Vote } from '@/lib/db/schema';
import { PencilEditIcon, SparklesIcon } from './icons';
import { Markdown } from './markdown';
import { MessageActions } from './message-actions';
import { PreviewAttachment } from './preview-attachment';
import { EmailVerificationToolResult } from './email-notification';
import { UserOnboardingToolResult } from './user-onboarding';
import { VerifyEmailVerificationCodeToolResult } from './verify-email-verification-code';

import equal from 'fast-deep-equal';
import { cn } from '@/lib/utils';
import { Button } from './ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { MessageEditor } from './message-editor';
import { MessageReasoning } from './message-reasoning';
import { UseChatHelpers } from '@ai-sdk/react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from './ui/alert-dialog';
import { Textarea } from './ui/textarea';

const PurePreviewMessage = ({
  chatId,
  message,
  vote,
  isLoading,
  setMessages,
  reload,
  isReadonly,
  append,
}: {
  chatId: string;
  message: UIMessage;
  vote: Vote | undefined;
  isLoading: boolean;
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
  isReadonly: boolean;
  append: UseChatHelpers['append'] | undefined;
}) => {
  const [mode, setMode] = useState<'view' | 'edit'>('view');

  return (
    <AnimatePresence>
      <motion.div
        data-testid={`message-${message.role}`}
        className="w-full mx-auto max-w-3xl px-4 group/message"
        initial={{ y: 5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        data-role={message.role}
      >
        <div
          className={cn(
            'flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl',
            {
              'w-full': mode === 'edit',
              'group-data-[role=user]/message:w-fit': mode !== 'edit',
            },
          )}
        >
          {message.role === 'assistant' && (
            <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border bg-background mt-4">
              <div className="translate-y-px">
                <Image
                  src="/images/chat-logo.jpg"
                  alt="Assistant"
                  width={40}
                  height={40}
                  className="rounded-full object-cover"
                />
              </div>
            </div>
          )}

          <div className="flex flex-col gap-2 w-full">
            <div
              data-testid={`message-attachments`}
              className="flex flex-row justify-end gap-2"
            >
              {message.experimental_attachments?.map((attachment) => (
                <PreviewAttachment
                  key={attachment.url}
                  attachment={attachment}
                />
              ))}
            </div>

            {message.parts?.map((part, index) => {
              const { type } = part;
              const key = `message-${message.id}-part-${index}`;

              if (type === 'reasoning') {
                return (
                  <MessageReasoning
                    key={key}
                    isLoading={isLoading}
                    reasoning={part.reasoning}
                  />
                );
              }

              if (type === 'text') {
                if (mode === 'view') {
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      {message.role === 'user' && !isReadonly && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              data-testid="message-edit-button"
                              variant="ghost"
                              className="px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100"
                              onClick={() => {
                                setMode('edit');
                              }}
                            >
                              <PencilEditIcon />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Edit message</TooltipContent>
                        </Tooltip>
                      )}

                      <div
                        data-testid="message-content"
                        className={cn('flex flex-col gap-4', {
                          'bg-primary text-primary-foreground px-3 py-2 rounded-xl':
                            message.role === 'user',
                        })}
                      >
                        <Markdown>{part.text}</Markdown>
                      </div>
                    </div>
                  );
                }

                if (mode === 'edit') {
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      <div className="size-8" />

                      <MessageEditor
                        key={message.id}
                        message={message}
                        setMode={setMode}
                        setMessages={setMessages}
                        reload={reload}
                      />
                    </div>
                  );
                }
              }

              if (type === 'tool-invocation') {
                const { toolInvocation } = part;
                const { toolName, toolCallId, state } = toolInvocation;

                if (state === 'call') {
                  const { args } = toolInvocation;

                  return (
                    <div key={toolCallId}>
                      {toolName === 'sendEmailVerification' ? (
                        <EmailVerificationToolResult loading={true} />
                      ) : toolName === 'userOnboarding' ? (
                        <UserOnboardingToolResult loading={true} />
                      ) : toolName === 'verifyEmailVerificationCode' ? (
                        <VerifyEmailVerificationCodeToolResult loading={true} />
                      ) : null}
                    </div>
                  );
                }

                if (state === 'result') {
                  const { result } = toolInvocation;

                  return (
                    <div key={toolCallId}>
                      {toolName === 'sendEmailVerification' ? (
                        <EmailVerificationToolResult
                          result={result}
                          loading={false}
                        />
                      ) : toolName === 'userOnboarding' ? (
                        <UserOnboardingToolResult
                          result={result}
                          loading={false}
                        />
                      ) : toolName === 'verifyEmailVerificationCode' ? (
                        <VerifyEmailVerificationCodeToolResult
                          result={result}
                          loading={false}
                        />
                      ) : (
                        <pre>{JSON.stringify(result, null, 2)}</pre>
                      )}
                    </div>
                  );
                }
              }
            })}

            {message.annotations?.find(
              (annotation) =>
                annotation &&
                typeof annotation === 'object' &&
                'type' in annotation &&
                annotation.type === 'status' &&
                'value' in annotation &&
                annotation.value === 'rate-limited',
            ) && (
              <div
                key={`message-${message.id}-annotation`}
                className="flex flex-row gap-2 items-start"
              >
                <div
                  data-testid="message-content"
                  className="flex flex-col gap-4"
                >
                  <Markdown>
                    You have been rate limited. Please wait for 24 hours.
                  </Markdown>
                </div>
              </div>
            )}

            {(message.annotations?.find(
              (annotation) =>
                annotation &&
                typeof annotation === 'object' &&
                'type' in annotation &&
                annotation.type === 'ask-for-confirmation' &&
                'messageId' in annotation &&
                annotation.messageId === message.id,
            ) ||
              message.parts?.find(
                (part) =>
                  part.type === 'tool-invocation' &&
                  part.toolInvocation.toolName === 'ask-for-confirmation' &&
                  part.toolInvocation.state === 'call',
              )) && <ConfirmationButtons append={append} />}

            {!isReadonly && (
              <MessageActions
                key={`action-${message.id}`}
                chatId={chatId}
                message={message}
                vote={vote}
                isLoading={isLoading}
              />
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export const PreviewMessage = memo(
  PurePreviewMessage,
  (prevProps, nextProps) => {
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.id !== nextProps.message.id) return false;
    if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;
    if (!equal(prevProps.vote, nextProps.vote)) return false;

    return true;
  },
);

export const ThinkingMessage = () => {
  const role = 'assistant';

  return (
    <motion.div
      data-testid="message-assistant-loading"
      className="w-full mx-auto max-w-3xl px-4 group/message "
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}
      data-role={role}
    >
      <div
        className={cx(
          'flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl',
          {
            'group-data-[role=user]/message:bg-muted': true,
          },
        )}
      >
        <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border">
          <Image
            src="/images/chat-logo.jpg"
            alt="Assistant"
            width={40}
            height={40}
            className="rounded-full"
          />
        </div>

        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-col gap-4 text-muted-foreground">
            I&apos;m thinking...
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const ConfirmationButtons = ({
  append,
}: {
  append: UseChatHelpers['append'] | undefined;
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [correctionText, setCorrectionText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isConfirmed, setIsConfirmed] = useState(false);

  const handleConfirm = async () => {
    if (isSubmitting || isConfirmed) return; // Prevent multiple submissions

    setIsSubmitting(true);
    try {
      if (append === undefined) return;

      // Add a user message to the chat
      await append({
        role: 'user',
        content: 'Yes, all details are correct.',
      });
      setIsConfirmed(true);
    } catch (error) {
      console.error('Error submitting confirmation:', error);
      setIsSubmitting(false); // Reset on error
    }
  };

  const handleCorrection = async () => {
    if (!correctionText.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      if (append === undefined) return;

      // Add a user message to the chat with corrections
      await append({
        role: 'user',
        content: `Not all details are correct. Corrections: ${correctionText}`,
      });
      setIsDialogOpen(false);
      setCorrectionText('');
    } catch (error) {
      console.error('Error submitting correction:', error);
      setIsSubmitting(false); // Reset on error
      // Keep dialog open on error
    }
  };

  return (
    <div
      key={`confirmation-buttons`}
      className="flex flex-row gap-2 items-start"
    >
      <div data-testid="message-content" className="flex flex-col gap-4">
        <div className="flex flex-col gap-3">
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              onClick={handleConfirm}
              disabled={isSubmitting || isConfirmed}
              className={`${
                isConfirmed
                  ? 'bg-green-500 hover:bg-green-500'
                  : 'bg-green-600 hover:bg-green-700'
              } disabled:bg-green-400 disabled:cursor-not-allowed text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 flex-1 sm:flex-none`}
            >
              {isConfirmed
                ? '✓ Confirmed!'
                : '✓ Confirm, all details are correct'}
            </Button>

            <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  disabled={isSubmitting || isConfirmed}
                  className="border-orange-300 text-orange-700 hover:bg-orange-50 hover:border-orange-400 disabled:opacity-50 disabled:cursor-not-allowed font-medium px-4 py-2 rounded-lg transition-colors duration-200 flex-1 sm:flex-none"
                >
                  ⚠️ Not all details are correct
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent className="max-w-md">
                <AlertDialogHeader>
                  <AlertDialogTitle>Correct the Details</AlertDialogTitle>
                  <AlertDialogDescription>
                    Please describe what information needs to be corrected. Be
                    as specific as possible.
                  </AlertDialogDescription>
                </AlertDialogHeader>

                <div className="py-4">
                  <Textarea
                    placeholder=""
                    value={correctionText}
                    onChange={(e) => setCorrectionText(e.target.value)}
                    className="min-h-[100px] resize-none"
                    autoFocus
                    disabled={isSubmitting}
                  />
                </div>

                <AlertDialogFooter>
                  <AlertDialogCancel
                    disabled={isSubmitting}
                    onClick={() => {
                      setCorrectionText('');
                      setIsDialogOpen(false);
                    }}
                  >
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleCorrection}
                    disabled={!correctionText.trim() || isSubmitting}
                    className="bg-orange-600 hover:bg-orange-700 disabled:bg-orange-400 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="size-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                        Submitting...
                      </>
                    ) : (
                      'Submit Corrections'
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>
    </div>
  );
};
