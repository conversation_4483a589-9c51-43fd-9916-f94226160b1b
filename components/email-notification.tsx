import {
  CheckCircle2Icon,
  XCircleIcon,
  Loader2,
  Mail,
  ChevronDownIcon,
  ChevronRightIcon,
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';

interface EmailVerificationToolResultProps {
  result?: { message?: string; error?: string; success?: boolean } | null;
  loading: boolean;
}

export function EmailVerificationToolResult({
  result,
  loading = false,
}: EmailVerificationToolResultProps) {
  const isMobile = useIsMobile();
  const [isExpanded, setIsExpanded] = useState(false);

  // Auto-expand on mobile for better visibility
  useEffect(() => {
    if (isMobile) {
      setIsExpanded(false);
    }
  }, [isMobile]);

  // More robust error detection - check for direct error property or success: false
  const hasError = Boolean(
    result?.error ||
      (result && 'success' in result && result.success === false),
  );

  // Determine status for display
  const getStatus = () => {
    if (loading) return 'loading';
    if (!result) return 'error';
    if (hasError) return 'error';
    return 'success';
  };

  const status = getStatus();

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="size-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle2Icon className="size-4 text-green-500" />;
      case 'error':
        return <XCircleIcon className="size-4 text-red-500" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'loading':
        return 'Sending verification code...';
      case 'success':
        return 'Verification code sent';
      case 'error':
        return 'Failed to send code';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.98 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.15 }}
      className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-950"
    >
      {/* Compact Header - Always Visible */}
      <button
        type="button"
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center gap-3 px-3 py-2.5 hover:bg-gray-50 dark:hover:bg-gray-900 transition-colors"
      >
        <Mail className="size-4 text-gray-500 dark:text-gray-400 shrink-0" />
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate sm:truncate-none">
            Email Verification
          </span>
          {getStatusIcon()}
        </div>
        <span className="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap hidden sm:block">
          {getStatusText()}
        </span>
        {isExpanded ? (
          <ChevronDownIcon className="size-4 text-gray-400 shrink-0" />
        ) : (
          <ChevronRightIcon className="size-4 text-gray-400 shrink-0" />
        )}
      </button>

      {/* Expandable Details */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.15, ease: 'easeInOut' }}
            className="overflow-hidden border-t border-gray-100 dark:border-gray-800"
          >
            <div className="p-3">
              {status === 'loading' && (
                <div className="flex items-start gap-3">
                  <Loader2 className="size-4 animate-spin text-blue-500 mt-0.5 shrink-0" />
                  <div className="flex flex-col gap-1 min-w-0">
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Processing request...
                    </span>
                    <span className="text-xs text-gray-600 dark:text-gray-300">
                      This may take a few moments
                    </span>
                  </div>
                </div>
              )}

              {status === 'success' && (
                <div className="flex items-start gap-3">
                  <CheckCircle2Icon className="size-4 text-green-500 mt-0.5 shrink-0" />
                  <div className="flex flex-col gap-1 min-w-0">
                    <span className="text-sm font-medium text-green-600 dark:text-green-400">
                      Email Sent Successfully
                    </span>
                    <span className="text-xs text-gray-600 dark:text-gray-300">
                      {result?.message ||
                        'A verification code has been sent to your email address. Please check your inbox and enter the code to continue.'}
                    </span>
                  </div>
                </div>
              )}

              {status === 'error' && (
                <div className="flex items-start gap-3">
                  <XCircleIcon className="size-4 text-red-500 mt-0.5 shrink-0" />
                  <div className="flex flex-col gap-1 min-w-0">
                    <span className="text-sm font-medium text-red-600 dark:text-red-400">
                      Email Send Failed
                    </span>
                    <span className="text-xs text-gray-600 dark:text-gray-300">
                      {result?.error ||
                        result?.message ||
                        'No response received from server'}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Please try again or contact support if the issue persists
                    </span>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
